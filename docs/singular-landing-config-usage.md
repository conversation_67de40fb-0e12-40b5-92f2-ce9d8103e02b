# Singular落地页配置管理系统使用指南

## 快速开始

### 1. 部署步骤

1. **执行数据库脚本**
   ```sql
   -- 在数据库中执行以下脚本
   source src/main/resources/sql/singular_landing_config.sql
   ```

2. **重启应用服务**
   ```bash
   # 重启Spring Boot应用
   ```

3. **验证部署**
   - 访问Swagger文档：`http://your-domain/swagger-ui.html`
   - 查找"Singular落地页配置管理"相关接口

### 2. 基本使用流程

#### 2.1 管理员操作流程

1. **登录系统**
   - 使用管理员账号登录

2. **新增配置**
   ```bash
   POST /product/singular/insert
   Content-Type: application/json
   
   {
     "configName": "游戏A落地页配置",
     "backgroundImage": "https://cdn.example.com/bg_game_a.jpg",
     "sdkKey": "game_a_sdk_key_001",
     "sdkSecret": "game_a_sdk_secret_001",
     "bundleId": "com.company.game_a",
     "baseLink": "https://download.example.com/game_a",
     "buttonGroups": "[{\"text\":\"立即下载\",\"url\":\"https://download.example.com/game_a/download\",\"style\":\"primary\"},{\"text\":\"了解更多\",\"url\":\"https://www.example.com/game_a/info\",\"style\":\"secondary\"}]",
     "status": 1,
     "remark": "游戏A的落地页配置"
   }
   ```

3. **查询配置列表**
   ```bash
   POST /product/singular/list
   Content-Type: application/json
   
   {
     "start": 1,
     "limit": 10,
     "status": 1
   }
   ```

4. **更新配置**
   ```bash
   POST /product/singular/update
   Content-Type: application/json
   
   {
     "id": 1,
     "configName": "游戏A落地页配置（已更新）",
     "backgroundImage": "https://cdn.example.com/bg_game_a_new.jpg",
     "status": 1
   }
   ```

5. **导出配置**
   ```bash
   POST /product/singular/export
   Content-Type: application/json
   
   {
     "status": 1
   }
   ```

#### 2.2 前端落地页使用流程

1. **根据SDK Key获取配置**
   ```javascript
   // 前端JavaScript示例
   fetch('/product/singular/config/sdk/game_a_sdk_key_001')
     .then(response => response.json())
     .then(data => {
       if (data.ret === 1) {
         const config = data.data;
         // 使用配置数据渲染落地页
         renderLandingPage(config);
       }
     });
   ```

2. **根据Bundle ID获取配置**
   ```javascript
   fetch('/product/singular/config/bundle/com.company.game_a')
     .then(response => response.json())
     .then(data => {
       if (data.ret === 1) {
         const config = data.data;
         renderLandingPage(config);
       }
     });
   ```

3. **根据URL参数获取配置**
   ```javascript
   // 从URL参数中获取sdkKey或bundleId
   const urlParams = new URLSearchParams(window.location.search);
   const sdkKey = urlParams.get('sdkKey');
   const bundleId = urlParams.get('bundleId');
   
   let apiUrl = '/product/singular/config?';
   if (sdkKey) {
     apiUrl += `sdkKey=${sdkKey}`;
   } else if (bundleId) {
     apiUrl += `bundleId=${bundleId}`;
   }
   
   fetch(apiUrl)
     .then(response => response.json())
     .then(data => {
       if (data.ret === 1) {
         renderLandingPage(data.data);
       }
     });
   ```

### 3. 前端落地页渲染示例

```javascript
function renderLandingPage(config) {
  // 设置背景图
  if (config.backgroundImage) {
    document.body.style.backgroundImage = `url(${config.backgroundImage})`;
  }
  
  // 解析按钮组
  if (config.buttonGroups) {
    const buttons = JSON.parse(config.buttonGroups);
    const buttonContainer = document.getElementById('button-container');
    
    buttons.forEach(button => {
      const btnElement = document.createElement('a');
      btnElement.href = button.url;
      btnElement.textContent = button.text;
      btnElement.className = `btn btn-${button.style}`;
      btnElement.target = '_blank';
      
      buttonContainer.appendChild(btnElement);
    });
  }
  
  // 设置基础链接
  if (config.baseLink) {
    const baseLinks = document.querySelectorAll('.base-link');
    baseLinks.forEach(link => {
      link.href = config.baseLink;
    });
  }
}
```

### 4. 常见问题

#### 4.1 配置不存在
**问题：** 调用获取配置接口返回"配置不存在"
**解决：** 
1. 检查SDK Key或Bundle ID是否正确
2. 确认配置状态是否为启用状态（status=1）
3. 联系管理员确认配置是否已创建

#### 4.2 按钮组格式错误
**问题：** 新增/更新配置时提示"按钮组信息格式错误"
**解决：** 确保按钮组信息是有效的JSON数组格式，例如：
```json
[
  {
    "text": "按钮文本",
    "url": "按钮链接",
    "style": "按钮样式"
  }
]
```

#### 4.3 唯一性约束冲突
**问题：** 新增配置时提示"SDK Key已存在"或"Bundle ID已存在"
**解决：** 
1. 检查是否已存在相同的SDK Key或Bundle ID
2. 使用不同的SDK Key或Bundle ID
3. 如果需要更新现有配置，使用更新接口而不是新增接口

### 5. 最佳实践

1. **命名规范**
   - 配置名称：使用有意义的名称，如"游戏名称_版本_环境"
   - SDK Key：使用项目前缀，如"project_name_sdk_key_001"
   - Bundle ID：遵循反向域名规范，如"com.company.product"

2. **按钮组设计**
   - 主要操作按钮使用"primary"样式
   - 次要操作按钮使用"secondary"样式
   - 边框按钮使用"outline"样式
   - 按钮数量建议不超过3个

3. **图片资源**
   - 背景图建议使用CDN链接
   - 图片尺寸建议适配移动端和PC端
   - 图片格式建议使用WebP或JPEG

4. **缓存策略**
   - 前端可以对配置数据进行适当缓存
   - 建议设置缓存过期时间，定期更新配置

### 6. 监控和维护

1. **日志监控**
   - 关注配置获取接口的调用频率和错误率
   - 监控配置管理接口的操作日志

2. **数据备份**
   - 定期备份配置数据
   - 重要配置变更前进行备份

3. **性能优化**
   - 对于高频访问的配置，考虑添加Redis缓存
   - 定期清理无效的配置数据
