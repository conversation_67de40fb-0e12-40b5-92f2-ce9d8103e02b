# Singular落地页配置管理API文档

## 概述

Singular落地页配置管理系统用于管理落地页的配置信息，支持运营同学直接配置生成落地页。系统提供完整的CRUD操作接口，以及供前端落地页使用的配置获取接口。

## 数据库表结构

### singular_landing_config 表

| 字段名 | 类型 | 长度 | 是否必填 | 说明 |
|--------|------|------|----------|------|
| id | bigint | 20 | 是 | 主键ID，自增 |
| config_name | varchar | 100 | 是 | 配置名称 |
| background_image | varchar | 500 | 否 | 背景图URL |
| sdk_key | varchar | 200 | 是 | SDK Key，唯一 |
| sdk_secret | varchar | 200 | 是 | SDK Secret |
| bundle_id | varchar | 200 | 是 | Bundle ID，唯一 |
| base_link | varchar | 500 | 是 | 基础链接 |
| button_groups | text | - | 否 | 按钮组信息(JSON格式) |
| status | tinyint | 2 | 否 | 状态：1-启用，0-禁用 |
| remark | varchar | 500 | 否 | 备注 |
| create_time | datetime | - | 否 | 创建时间 |
| update_time | datetime | - | 否 | 更新时间 |
| create_user | varchar | 64 | 否 | 创建人 |
| update_user | varchar | 64 | 否 | 更新人 |

## API接口

### 1. 分页查询配置列表

**接口地址：** `GET /product/singular/list`

**请求参数：**
```json
{
  "configName": "配置名称（可选，模糊查询）",
  "sdkKey": "SDK Key（可选，模糊查询）",
  "bundleId": "Bundle ID（可选，模糊查询）",
  "status": 1,
  "start": 1,
  "limit": 100
}
```

**响应示例：**
```json
{
  "ret": 1,
  "msg": "操作成功",
 "total": 100,
 "data": [
   {
     "id": 1,
     "configName": "测试配置1",
     "backgroundImage": "https://example.com/bg1.jpg",
     "sdkKey": "test_sdk_key_001",
     "sdkSecret": "test_sdk_secret_001",
     "bundleId": "com.example.app1",
     "baseLink": "https://example.com/base1",
     "buttonGroups": "[{\"text\":\"下载游戏\",\"url\":\"https://example.com/download1\",\"style\":\"primary\"}]",
     "status": 1,
     "remark": "测试用配置",
     "createTime": "2025-01-27 10:00:00",
     "updateTime": "2025-01-27 10:00:00",
     "createUser": "admin",
     "updateUser": null
   }
 ]
}
```

### 2. 查询配置详情

**接口地址：** `GET /product/singular/detail/{id}`

**路径参数：**
- id: 配置ID

**响应示例：**
```json
{
  "ret": 1,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "configName": "测试配置1",
    "backgroundImage": "https://example.com/bg1.jpg",
    "sdkKey": "test_sdk_key_001",
    "sdkSecret": "test_sdk_secret_001",
    "bundleId": "com.example.app1",
    "baseLink": "https://example.com/base1",
    "buttonGroups": "[{\"text\":\"下载游戏\",\"url\":\"https://example.com/download1\",\"style\":\"primary\"}]",
    "status": 1,
    "remark": "测试用配置"
  }
}
```

### 3. 新增配置

**接口地址：** `POST /product/singular/insert`

**请求参数：**
```json
{
  "configName": "新配置",
  "backgroundImage": "https://example.com/bg.jpg",
  "sdkKey": "new_sdk_key",
  "sdkSecret": "new_sdk_secret",
  "bundleId": "com.example.newapp",
  "baseLink": "https://example.com/base",
  "buttonGroups": "[{\"text\":\"下载\",\"url\":\"https://example.com/download\",\"style\":\"primary\"}]",
  "status": 1,
  "remark": "新增的配置"
}
```

**响应示例：**
```json
{
  "ret": 1,
  "msg": "新增成功"
}
```

### 4. 更新配置

**接口地址：** `POST /product/singular/update`

**请求参数：**
```json
{
  "id": 1,
  "configName": "更新后的配置",
  "backgroundImage": "https://example.com/new_bg.jpg",
  "status": 1,
  "remark": "更新后的备注"
}
```

**响应示例：**
```json
{
  "ret": 1,
  "msg": "更新成功"
}
```

### 5. 删除配置

**接口地址：** `DELETE /product/singular/delete/{id}`

**路径参数：**
- id: 配置ID

**响应示例：**
```json
{
  "ret": 1,
  "msg": "删除成功"
}
```

### 6. 批量删除配置

**接口地址：** `POST /product/singular/batchDelete`

**请求参数：**
```json
[1, 2, 3]
```

**响应示例：**
```json
{
  "ret": 1,
  "msg": "批量删除成功，共删除3条记录"
}
```

### 7. 导出配置列表

**接口地址：** `POST /product/singular/export`

**请求参数：**
```json
{
  "configName": "配置名称（可选，模糊查询）",
  "sdkKey": "SDK Key（可选，模糊查询）",
  "bundleId": "Bundle ID（可选，模糊查询）",
  "status": 1
}
```

**说明：** 此接口直接返回Excel文件下载，无JSON响应。

### 8. 根据URL参数获取配置（供落地页使用）

**接口地址：** `GET /product/singular/config`

**查询参数：**
- sdkKey: SDK Key（可选）
- bundleId: Bundle ID（可选）

**说明：** 优先使用sdkKey查询，如果sdkKey为空则使用bundleId查询。此接口无需登录验证。

**使用示例：**
```
GET /product/singular/config?sdkKey=test_sdk_key_001
GET /product/singular/config?bundleId=com.example.app1
```

**响应示例：**
```json
{
  "ret": 1,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "configName": "测试配置1",
    "backgroundImage": "https://example.com/bg1.jpg",
    "sdkKey": "test_sdk_key_001",
    "sdkSecret": "test_sdk_secret_001",
    "bundleId": "com.example.app1",
    "baseLink": "https://example.com/base1",
    "buttonGroups": "[{\"text\":\"下载游戏\",\"url\":\"https://example.com/download1\",\"style\":\"primary\"}]",
    "status": 1,
    "remark": "测试用配置"
  }
}
```

### 9. 根据SDK Key获取配置

**接口地址：** `GET /product/singular/config/sdk/{sdkKey}`

**说明：** 此接口无需登录验证，返回格式同上。

### 10. 根据Bundle ID获取配置

**接口地址：** `GET /product/singular/config/bundle/{bundleId}`

**说明：** 此接口无需登录验证，返回格式同上。

## 按钮组JSON格式说明

按钮组信息使用JSON数组格式存储，每个按钮包含以下字段：

```json
[
  {
    "text": "按钮文本",
    "url": "按钮链接",
    "style": "按钮样式（primary/secondary/outline等）"
  }
]
```

**示例：**
```json
[
  {
    "text": "下载游戏",
    "url": "https://example.com/download",
    "style": "primary"
  },
  {
    "text": "了解更多",
    "url": "https://example.com/more",
    "style": "secondary"
  },
  {
    "text": "分享好友",
    "url": "https://example.com/share",
    "style": "outline"
  }
]
```

## 部署说明

1. 执行SQL脚本创建数据库表：
   ```sql
   -- 执行 src/main/resources/sql/singular_landing_config.sql
   ```

2. 重启应用服务

3. 访问Swagger文档查看接口：
   ```
   http://your-domain/swagger-ui.html
   ```

## 注意事项

1. SDK Key和Bundle ID具有唯一性约束
2. 按钮组信息必须是有效的JSON数组格式
3. 供落地页使用的配置获取接口无需登录验证
4. 管理接口需要登录验证
5. 所有接口都支持跨域访问
