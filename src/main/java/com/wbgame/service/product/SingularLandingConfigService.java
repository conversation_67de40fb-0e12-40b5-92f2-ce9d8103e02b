package com.wbgame.service.product;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.product.SingularLandingConfig;

import java.util.List;

/**
 * @Description Singular落地页配置Service接口
 * @Date 2025/06/27
 */
public interface SingularLandingConfigService {

    /**
     * 查询配置列表数据
     *
     * @param config 查询条件
     * @return 配置列表
     */
    List<SingularLandingConfig> queryListData(SingularLandingConfig config);

    /**
     * 根据ID查询配置详情
     *
     * @param id 主键ID
     * @return 配置详情
     */
    SingularLandingConfig queryByIdData(Long id);

    /**
     * 根据SDK Key获取配置（供落地页使用）
     *
     * @param sdkKey SDK Key
     * @return 配置信息
     */
    SingularLandingConfig getConfigBySdkKeyData(String sdkKey);

    /**
     * 根据Bundle ID获取配置（供落地页使用）
     *
     * @param bundleId Bundle ID
     * @return 配置信息
     */
    SingularLandingConfig getConfigByBundleIdData(String bundleId);

    /**
     * 新增配置
     *
     * @param config 配置信息
     * @return 影响行数
     */
    int insertData(SingularLandingConfig config);

    /**
     * 更新配置
     *
     * @param config 配置信息
     * @return 影响行数
     */
    int updateData(SingularLandingConfig config);

    /**
     * 删除配置
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByIdData(Long id);

    /**
     * 批量删除配置
     *
     * @param ids 主键ID列表
     * @return 影响行数
     */
    int batchDeleteData(List<Long> ids);

    /**
     * 校验配置数据
     *
     * @param config 配置信息
     * @param isUpdate 是否为更新操作
     * @return 校验结果，null表示校验通过，非null表示错误信息
     */
    String validateConfigData(SingularLandingConfig config, boolean isUpdate);

}
