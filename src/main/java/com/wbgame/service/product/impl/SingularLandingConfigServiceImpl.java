package com.wbgame.service.product.impl;

import com.alibaba.fastjson.JSONArray;
import com.wbgame.mapper.master.product.SingularLandingConfigMapper;
import com.wbgame.pojo.product.SingularLandingConfig;
import com.wbgame.service.product.SingularLandingConfigService;
import com.wbgame.utils.BlankUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description Singular落地页配置Service实现类
 * @Date 2025/06/27
 */
@Service
public class SingularLandingConfigServiceImpl implements SingularLandingConfigService {

    private static final Logger logger = LoggerFactory.getLogger(SingularLandingConfigServiceImpl.class);

    @Autowired
    private SingularLandingConfigMapper singularLandingConfigMapper;

    @Override
    public List<SingularLandingConfig> queryListData(SingularLandingConfig config) {
        return singularLandingConfigMapper.queryList(config);
    }

    @Override
    public SingularLandingConfig queryByIdData(Long id) {
        return singularLandingConfigMapper.queryById(id);
    }

    @Override
    public SingularLandingConfig getConfigBySdkKeyData(String sdkKey) {
        return singularLandingConfigMapper.queryBySdkKey(sdkKey);
    }

    @Override
    public SingularLandingConfig getConfigByBundleIdData(String bundleId) {
        return singularLandingConfigMapper.queryByBundleId(bundleId);
    }

    @Override
    public int insertData(SingularLandingConfig config) {
        return singularLandingConfigMapper.insert(config);
    }

    @Override
    public int updateData(SingularLandingConfig config) {
        return singularLandingConfigMapper.update(config);
    }

    @Override
    public int deleteByIdData(Long id) {
        return singularLandingConfigMapper.deleteById(id);
    }

    @Override
    public int batchDeleteData(List<Long> ids) {
        return singularLandingConfigMapper.batchDelete(ids);
    }

    @Override
    public String validateConfigData(SingularLandingConfig config, boolean isUpdate) {
        try {
            // 必填字段校验
            if (BlankUtils.checkBlank(config.getConfigName())) {
                return "配置名称不能为空";
            }
            if (BlankUtils.checkBlank(config.getSdkKey())) {
                return "SDK Key不能为空";
            }
            if (BlankUtils.checkBlank(config.getSdkSecret())) {
                return "SDK Secret不能为空";
            }
            if (BlankUtils.checkBlank(config.getBundleId())) {
                return "Bundle ID不能为空";
            }
            if (BlankUtils.checkBlank(config.getBaseLink())) {
                return "基础链接不能为空";
            }
            
            // 按钮组JSON格式校验
            if (!BlankUtils.checkBlank(config.getButtonGroups())) {
                try {
                    JSONArray.parseArray(config.getButtonGroups());
                } catch (Exception e) {
                    return "按钮组信息格式错误，必须为有效的JSON数组";
                }
            }
            
//             唯一性校验
//            Long excludeId = isUpdate ? config.getId() : null;
//
//            // SDK Key唯一性校验
//            int sdkKeyCount = singularLandingConfigMapper.checkSdkKeyExists(config.getSdkKey(), excludeId);
//            if (sdkKeyCount > 0) {
//                return "SDK Key已存在";
//            }
//
//            // Bundle ID唯一性校验
//            int bundleIdCount = singularLandingConfigMapper.checkBundleIdExists(config.getBundleId(), excludeId);
//            if (bundleIdCount > 0) {
//                return "Bundle ID已存在";
//            }
            
            return null; // 校验通过
        } catch (Exception e) {
            logger.error("校验Singular落地页配置失败", e);
            return "校验失败";
        }
    }
}
