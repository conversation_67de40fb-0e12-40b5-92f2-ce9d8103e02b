package com.wbgame.controller;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.wbgame.common.RefeshCacheConstant;
import org.apache.http.client.methods.HttpGet;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.mapper.clean.master.CleanYdMapper;
import com.wbgame.mapper.tfxt.TfxtMapper;
import com.wbgame.pojo.AdPrjVo;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.DnAdConfigVo;
import com.wbgame.pojo.ExtendVo;
import com.wbgame.pojo.NpActiveFree;
import com.wbgame.pojo.NpPostVo;
import com.wbgame.pojo.clean.HttpRequester;
import com.wbgame.pojo.clean.HttpRespons;
import com.wbgame.pojo.push.ConfigVo;
import com.wbgame.service.AdService;
import com.wbgame.service.SomeService;
import com.wbgame.service.YdService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpClientUtils;
import com.wbgame.utils.JxlUtil;

import jxl.Sheet;
import jxl.Workbook;



///**
// * 广告配置
// * <AUTHOR> -待删除
// */
//@Controller
public class ApkConfigController {

    @Autowired
    SomeService someService;
    @Autowired
    AdService adService;
    @Autowired
    TfxtMapper tfxtMapper;
    @Autowired
    CleanYdMapper cleanYdMapper;
    @Autowired
	private YdService ydService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public static String doGet(String httpurl) {
        HttpURLConnection connection = null;
        InputStream is = null;
        BufferedReader br = null;
        String result = null;// 返回结果字符串
        try {
            // 创建远程url连接对象
            URL url = new URL(httpurl);
            // 通过远程url连接对象打开一个连接，强转成httpURLConnection类
            connection = (HttpURLConnection) url.openConnection();
            // 设置连接方式：get
            connection.setRequestMethod("GET");
            // 设置连接主机服务器的超时时间：15000毫秒
            connection.setConnectTimeout(15000);
            // 设置读取远程返回的数据时间：60000毫秒
            connection.setReadTimeout(60000);
            // 发送请求
            connection.connect();
            // 通过connection连接，获取输入流
            if (connection.getResponseCode() == 200) {
                is = connection.getInputStream();
                // 封装输入流is，并指定字符集
                br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
                // 存放数据
                StringBuffer sbf = new StringBuffer();
                String temp ;
                while ((temp = br.readLine()) != null) {
                    sbf.append(temp);
                    sbf.append("\r\n");
                }
                result = sbf.toString();
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 关闭资源
            if (null != br) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            if (null != is) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            connection.disconnect();// 关闭远程连接
        }

        return result;
    }

    /**
     * 刷新缓存
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="/refushApiAdcache", method={RequestMethod.POST})
    public @ResponseBody
    String refushApiAdcache(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
//        String res;
//
//        String res1 = doGet("http://172.18.50.115:6601/refeshCache?mapid=6");
//        String res2 = doGet("http://172.18.50.115:6602/refeshCache?mapid=6");
//        String res3 = doGet("http://172.18.50.111:6601/refeshCache?mapid=6");
//
//        String res4 = doGet("http://172.18.50.111:6602/refeshCache?mapid=6");
////        String res5 = doGet("http://172.18.50.115:6605/refeshCache?mapid=6");
////        String res6 = doGet("http://172.18.50.115:6606/refeshCache?mapid=6");
////        String res7 = doGet("http://172.18.50.115:6607/refeshCache?mapid=6");
////        String res8 = doGet("http://172.18.50.115:6608/refeshCache?mapid=6");
////        String res9 = doGet("http://172.18.50.115:6609/refeshCache?mapid=6");
////        String res10 = doGet("http://172.18.50.115:6610/refeshCache?mapid=6");
//        if(!"ok".equals(res1.trim()) || !"ok".equals(res2.trim()) || !"ok".equals(res3.trim()) || !"ok".equals(res4.trim())
////                || !"ok".equals(res5.trim()) || !"ok".equals(res6.trim()) || !"ok".equals(res7.trim()) || !"ok".equals(res8.trim())
////                || !"ok".equals(res9.trim()) || !"ok".equals(res10.trim())
//                ){
//            res = "fail";
//        }else {
//            res = "ok";
//        }
        JSONObject result = new JSONObject();
        // 将结果以json对象输出
        result.put("msg", "ok");
        return result.toJSONString();
    }

    /**
     * 新增操作广告配置时自动刷新缓存功能
     * @return
     */
    private int refushCacheByPid(String pid){
//        //27:新版广告开通配置
//        //外网地址
//        String result1=doGet("http://47.107.140.126:6401/RefeshCacheServlet?mapid=27&val="+pid);
//        String result2=doGet("http://47.107.140.126:6402/RefeshCacheServlet?mapid=27&val="+pid);
//        String result3=doGet("http://47.107.140.126:6403/RefeshCacheServlet?mapid=27&val="+pid);
//        String result4=doGet("http://47.107.140.126:6404/RefeshCacheServlet?mapid=27&val="+pid);
////        String result5=doGet("http://47.107.140.126:6405/RefeshCacheServlet?mapid=27&val="+pid);
//
//        String result11=doGet("http://47.107.160.122:6401/RefeshCacheServlet?mapid=27&val="+pid);
//        String result12=doGet("http://47.107.160.122:6402/RefeshCacheServlet?mapid=27&val="+pid);
//        String result13=doGet("http://47.107.160.122:6403/RefeshCacheServlet?mapid=27&val="+pid);
//        String result14=doGet("http://47.107.160.122:6404/RefeshCacheServlet?mapid=27&val="+pid);
////        String result15=doGet("http://47.107.160.122:6405/RefeshCacheServlet?mapid=27&val="+pid);
//
//
//        if(result1.contains("ok")
//                &&result2.contains("ok")
//                &&result3.contains("ok")
//                &&result4.contains("ok")
////                &&result5.contains("ok")
//                &&result11.contains("ok")
//                &&result12.contains("ok")
//                &&result13.contains("ok")
//                &&result14.contains("ok")
////                &&result15.contains("ok")
//                )
//        {
//            return 1;
//        }
        return 1;
    }

    /**
     * 新版广告开通配置 测试专用
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="/selectNewAdOpenConfigTest", method={RequestMethod.POST})
    public @ResponseBody
    String selectNewAdOpenConfigTest(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try{
            String start = BlankUtils.checkNull(request, "start");
            String limit = BlankUtils.checkNull(request, "limit");

            if(BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(cu.getToken());
            if(cuser == null){
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }else{
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // 当前页
            int pageNo = (pageStart / pageSize) + 1;
            
            String appid = BlankUtils.checkNull(request, "appid");
            String ad_sid = BlankUtils.checkNull(request, "ad_sid");
            String prjid = BlankUtils.checkNull(request, "ad_bd_sid");
            String status = BlankUtils.checkNull(request, "app_id");
            String sc_name = BlankUtils.checkNull(request, "sc_name");
            Map<String, String> parmMap = new HashMap<>();
            parmMap.put("appid", appid);
            parmMap.put("ad_sid", ad_sid);
            parmMap.put("prjid", prjid);
            parmMap.put("status", status);
            parmMap.put("sc_name", sc_name);
            if("technical_test".equals(cuser.getOrg_id())){ // 技术部测试 112233,445561,778898,3333开头id
                String testids = "'112233','445561','778898'";
                if(StringUtils.isEmpty(prjid)
                		|| (!testids.contains(prjid) && !prjid.startsWith("3333"))){
                	// 添加额外条件
                	parmMap.remove("prjid");
                    parmMap.put("testids", testids);
                }
            }
            
            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<ConfigVo> list = someService.selectNewAdOpenConfigForSqlTest(parmMap);
            long size = ((Page) list).getTotal();
            
            JSONObject result = new JSONObject();
            result.put("data", list);
            result.put("ret",1);
            result.put("totalCount", size);
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 新版广告开通配置 修改新增 测试专用
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="/handleNewAdOpenConfigTest", method={RequestMethod.POST})
    public @ResponseBody
    String handleNewAdOpenConfigTest(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try{
            if(BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(cu.getToken());
            if(cuser == null){
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }else{
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            int ret = 0;
            String appid = BlankUtils.checkNull(request, "appid");
            String status = BlankUtils.checkNull(request, "status");
            String ad_id = BlankUtils.checkNull(request, "ad_id");
            String addprjmid = BlankUtils.checkNull(request, "addprjmid");
            String adtype = BlankUtils.checkNull(request, "adtype");
            String add_pos = BlankUtils.checkNull(request, "add_pos");
            String add_rate = BlankUtils.checkNull(request,"add_rate");
            String add_agentpecent = BlankUtils.checkNull(request, "add_agentpecent");
            //String add_delaytime = BlankUtils.checkNull(request, "add_delaytime");
            String add_round = BlankUtils.checkNull(request, "add_round");
            String city_statu = BlankUtils.checkNull(request, "city_statu");
            String cityid = BlankUtils.checkNull(request, "cityid");
            String add_telecom = BlankUtils.checkNull(request, "add_telecom");
            String handle = BlankUtils.checkNull(request, "handle");
            String seqid = BlankUtils.checkNull(request, "seqid");
            String limit_num = BlankUtils.checkNull(request,"limit_num");
            //String limit_date = BlankUtils.checkNull(request,"limit_date");
            String limit_second = BlankUtils.checkNull(request, "limit_second");
            String showmodel = BlankUtils.checkNull(request, "showmodel");
            ConfigVo infovo=new ConfigVo();
            infovo.setAppid(appid);
            infovo.setActiv_cityid(cityid);
            infovo.setActiv_statu(city_statu);
            infovo.setActiv_telecom(add_telecom);
            infovo.setAd_sid_str(ad_id);
            infovo.setAgentpecent(add_agentpecent);

            infovo.setDelaytime("0");
            infovo.setName(add_pos);
            infovo.setPrjmid(addprjmid);
            infovo.setRate("100");
            infovo.setRound(add_round);
            infovo.setStatu(status);
            infovo.setType(adtype);
            infovo.setSeqid(seqid);
            infovo.setRate(add_rate);
            infovo.setLimit_num(limit_num);
            infovo.setLimit_date("0");
            infovo.setLimit_second(limit_second);
            infovo.setShowmodel(showmodel);
            if("add".equals(handle)){
            	String ins = "insert into TEST_EXTEND_ADCONFIG(appid,prjmid,name,type,rate,limit_num,ad_sid_str,agentpecent,round,statu,"
					+" delaytime,activ_cityid,activ_telecom,activ_statu,createdate,delaydays,delaysecond,showmodel) "
					+" values (#{obj.appid},#{obj.prjmid},#{obj.name},#{obj.type},#{obj.rate},#{obj.limit_num},#{obj.ad_sid_str},#{obj.agentpecent},#{obj.round},#{obj.statu},"
					+" #{obj.delaytime},#{obj.activ_cityid},#{obj.activ_telecom},#{obj.activ_statu},now(),#{obj.limit_date},#{obj.limit_second},#{obj.showmodel})";
            	ret = adService.execSqlHandle(ins, infovo);
            	
                // ret = someService.insertNewAdOpenConfig(infovo);
            }
            if("update".equals(handle)){
            	String update = "update TEST_EXTEND_ADCONFIG set appid=#{obj.appid},prjmid=#{obj.prjmid},type=#{obj.type},ad_sid_str=#{obj.ad_sid_str},name=#{obj.name},"
        			+" rate=#{obj.rate},limit_num=#{obj.limit_num},agentpecent=#{obj.agentpecent},statu=#{obj.statu},round=#{obj.round},"
        			+" delaytime=#{obj.delaytime},activ_telecom=#{obj.activ_telecom},activ_cityid=#{obj.activ_cityid},"
        			+" activ_statu=#{obj.activ_statu},delaydays=#{obj.limit_date},delaysecond=#{obj.limit_second},"
        			+" showmodel=#{obj.showmodel} where seqid=#{obj.seqid}";
            	ret = adService.execSqlHandle(update, infovo);
            	
                // ret = someService.updateNewAdOpenConfig(infovo);
            }
            
            JSONObject result = new JSONObject();
            if(ret > 0){
                result.put("ret",1);
                result.put("msg","操作成功");
            }else {
                result.put("ret",0);
                result.put("msg","操作失败");
            }
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 新版广告开通配置 删除 测试专用
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="/deleteNewAdOpenConfigTest", method={RequestMethod.POST})
    public @ResponseBody
    String deleteNewAdOpenConfigTest(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try{
            if(BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(cu.getToken());
            if(cuser == null){
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }else{
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            String seqids = BlankUtils.checkNull(request, "seqid");
            if(BlankUtils.sqlValidate(seqids)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}
            
            String del = "delete from TEST_EXTEND_ADCONFIG where SEQID in ("+seqids+")";
            int res = adService.execSql(del);
            
            JSONObject result = new JSONObject();
            if(res > 0){
                result.put("ret",1);
                result.put("msg","删除成功");
            }else {
                result.put("ret",0);
                result.put("msg","删除失败");
            }
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 新版广告开通配置 打开/关闭  测试专用
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="/openNewAdOpenConfigTest", method={RequestMethod.POST})
    public @ResponseBody
    String openNewAdOpenConfigTest(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try{
            if(BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(cu.getToken());
            if(cuser == null){
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }else{
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            String ad_sids = BlankUtils.checkNull(request, "ad_sids");
            String statu = BlankUtils.checkNull(request, "statu");
            if(statu.equals("0")){
                statu = "1";
            }else{
                statu = "0";
            }
            
            if(BlankUtils.sqlValidate(ad_sids)
            	|| BlankUtils.sqlValidate(statu)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}
            
            String open = "update TEST_EXTEND_ADCONFIG set statu = '"+statu+"' where seqid in ("+ad_sids+")";
            int res = adService.execSql(open);
            
            JSONObject result = new JSONObject();
            if(res > 0){
                result.put("ret",1);
                result.put("msg","操作成功");
            }else {
                result.put("ret",0);
                result.put("msg","操作失败");
            }
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 广告插屏刷新缓存
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="/refushcacheisnum", method={RequestMethod.POST})
    public @ResponseBody
    String refushcacheisnum(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        int res = -1;
        for (int port = 6401; port <= 6405; port++) {

            String res1 = doGet("http://172.18.50.124:6401/RefeshCacheServlet?mapid=29");
            String res2 = doGet("http://172.18.50.124:6402/RefeshCacheServlet?mapid=29");
            String res3 = doGet("http://172.18.50.124:6403/RefeshCacheServlet?mapid=29");
            String res4 = doGet("http://172.18.50.124:6404/RefeshCacheServlet?mapid=29");
            String res5 = doGet("http://172.18.50.124:6405/RefeshCacheServlet?mapid=29");
            
            String res6 = doGet("http://172.18.50.123:6401/RefeshCacheServlet?mapid=29");
            String res7 = doGet("http://172.18.50.123:6402/RefeshCacheServlet?mapid=29");
            String res8 = doGet("http://172.18.50.123:6403/RefeshCacheServlet?mapid=29");
            String res9 = doGet("http://172.18.50.123:6404/RefeshCacheServlet?mapid=29");
            String res10 = doGet("http://172.18.50.123:6405/RefeshCacheServlet?mapid=29");
            if(!"ok".equals(res1.trim()) || !"ok".equals(res2.trim()) || !"ok".equals(res3.trim()) || !"ok".equals(res4.trim())
                    || !"ok".equals(res5.trim()) || !"ok".equals(res6.trim()) || !"ok".equals(res7.trim()) || !"ok".equals(res8.trim())
                    || !"ok".equals(res9.trim()) || !"ok".equals(res10.trim())){
                res = 0;
                break;
            }
            res = 1;
        }
        JSONObject result = new JSONObject();
        // 将结果以json对象输出
        result.put("msg", res);
        return result.toJSONString();
    }

}