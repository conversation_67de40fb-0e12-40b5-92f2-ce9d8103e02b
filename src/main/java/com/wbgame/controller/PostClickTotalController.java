package com.wbgame.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;
// -待删除
//@Controller
public class PostClickTotalController {
	
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	
	@CrossOrigin
	@RequestMapping(value="/postclick/jrtt", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String postClick(HttpServletRequest req, HttpServletResponse resp){
		
		try {
			String replace = req.getParameter("value").replace(" ", "+");
			String value = new String(Base64.decodeBase64(replace),"UTF-8");
			JSONObject obj = JSONObject.parseObject(value);
			if(!BlankUtils.isNumeric(obj.getString("appid"))
					|| BlankUtils.checkBlank(obj.getString("lsn"))
					|| BlankUtils.checkBlank(obj.getString("clickid"))
					|| BlankUtils.checkBlank(obj.getString("event_type"))){
					
				return "{\"ret\":0,\"msg\":\"request value is error!\"}";
			}
			
			redisTemplate.opsForHash().putIfAbsent(CommonUtil.REDIS_HASH_POSTCLICK_ADINFO,
					obj.getString("appid")+"#_&"+obj.getString("lsn"), 
					obj.getString("clickid")+"#_&"+obj.getString("event_type"));
			
			JSONObject result = new JSONObject();
			result.put("ret", 1);
			result.put("msg", "success");
			return result.toJSONString();
		} catch (Exception e) {
			e.printStackTrace();
			JSONObject result = new JSONObject();
			result.put("ret", 0);
			result.put("msg", "错误信息 : "+e.getMessage());
			return result.toJSONString();
		}
	}
	
}
