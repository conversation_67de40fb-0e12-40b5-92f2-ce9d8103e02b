package com.wbgame.controller;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.digest.DigestUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.mapper.master.PushMapper;
import com.wbgame.pojo.ApkUserTotalVo;
import com.wbgame.pojo.custom.ExtendAdposConfVo;
import com.wbgame.pojo.custom.GDTWorkReportVo;
import com.wbgame.pojo.custom.HomeDiamondVo;
import com.wbgame.pojo.custom.WxPushFilterVo;
import com.wbgame.service.AdService;
import com.wbgame.service.AdmsgService;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;
import com.wbgame.utils.HttpClientUtils;
import com.wbgame.utils.JxlUtil;

///**
// * 广告支付接口相关 -待删除
// *
// * <AUTHOR>
// */
//@Controller
public class AdPayController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private AdService adService;
    @Autowired
    private AdmsgService admsgService;
    @Autowired
    private SomeService someService;

    // 重叠用户
    @CrossOrigin
    @RequestMapping(value = "/selectRepeatUserTotal", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String selectRepeatUserTotal(HttpServletRequest request, HttpServletResponse response) {
        String appid = BlankUtils.checkNull(request, "appid");
        String matchappid = BlankUtils.checkNull(request, "matchappid");
        String createtime = BlankUtils.checkNull(request, "createtime");
        String endtime = BlankUtils.checkNull(request, "endtime");

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        try {
            JSONObject result = new JSONObject();
            List<Map<String, Object>> resultList = new ArrayList<>();
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("appid", appid);
            paramMap.put("beginDt", createtime);
            paramMap.put("endDt", endtime);
            List<ApkUserTotalVo> list = someService.selectApkUserTrendLine(paramMap);

            int days = Days.daysBetween(DateTime.parse(createtime), DateTime.parse(endtime)).getDays();
            List<String> sqlList = new ArrayList<String>();
            for (int i = 0; i <= days; i++) {
                String tdate = DateTime.parse(createtime).plusDays(i).toString("yyyy-MM-dd");
                String sql = "select '" + tdate + "' mapkey,count(lsn) as counts from (" +
                        " select lsn,count(productid) pn from NP_POST_LOG_TWO_" + tdate.replace("-", "") +
                        " where productid = '" + appid + "' or productid = '" + matchappid + "'" +
                        " group by lsn having(count(productid) > 1)" +
                        ")";
                sqlList.add(sql);
            }
            String sqls = String.join(" union all ", sqlList);
            Map<String, Map<String, Object>> queryListMap = adService.queryListMapOfKey(sqls);

            // 加入缓存中减少查询时间
            List<String> nums = (List<String>) redisTemplate.opsForValue().get("wb_oldrepeatnum_" + appid + matchappid + "_" + DateTime.now().toString("yyyyMMdd"));
            if (nums == null) {
                nums = new ArrayList<String>();

                // 历史用户查询
//	            String query1 = "select count(lsn) counts from zyzhfee.push_user_info_two where product_id = '"+appid+"'";
//	            String num1 = pushMapper.queryListMap(query1).get(0).get("COUNTS")+"";
//	            nums.add(num1);
//	            
//	            // 历史重叠用户查询
//	            String query2 = "select count(lsn) counts from ( "+
//									"select lsn,count(product_id) pn from zyzhfee.push_user_info_two "+
//									"where product_id = '"+appid+"' or product_id = '"+matchappid+"' "+
//									"group by lsn having(count(product_id) > 1) "+
//								")";
//	            String num2 = pushMapper.queryListMap(query2).get(0).get("COUNTS")+"";
//	            nums.add(num2);
//	            redisTemplate.opsForValue().set(
//	            		"wb_oldrepeatnum_"+appid+matchappid+"_"+DateTime.now().toString("yyyyMMdd"), nums, 30, TimeUnit.MINUTES);
            }
            int oldNum = Integer.valueOf(nums.get(0));
            int oldRepeatNum = Integer.valueOf(nums.get(1));

            list.forEach(cs -> {
                int repeatNum = 0;
                Map<String, Object> countMap = queryListMap.get(cs.getCreatetime());
                if (countMap != null && countMap.size() > 0)
                    repeatNum = Integer.valueOf(countMap.get("COUNTS") + "");

                Map<String, Object> rmap = new HashMap<>();
                rmap.put("tdate", cs.getCreatetime());
                rmap.put("appName", cs.getAppName());
                rmap.put("currDau", cs.getCurrDau());
                rmap.put("repeatNum", repeatNum);
                rmap.put("repeatRate", BlankUtils.getNumFormat((repeatNum * 100.00 / cs.getCurrDau()), 1) + "%");
                rmap.put("oldNum", oldNum);
                rmap.put("oldRepeatNum", oldRepeatNum);
                rmap.put("oldRepeatRate", BlankUtils.getNumFormat((oldRepeatNum * 100.00 / oldNum), 1) + "%");
                resultList.add(rmap);
            });
            redisTemplate.opsForValue().set("repeatUserTotalExcelList" + token, resultList, 20, TimeUnit.MINUTES);

            result.put("ret", 1);
            result.put("data", resultList);
            return result.toString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
            return result.toString();
        }
    }

    @CrossOrigin
    @RequestMapping(value = "/exportRepeatUserTotal", method = RequestMethod.POST)
    public void exportRepeatUserTotal(String token, HttpServletRequest request, HttpServletResponse response) {

        List<Map<String, Object>> contentList =
                (List<Map<String, Object>>) redisTemplate.opsForValue().get("repeatUserTotalExcelList" + token);

        //数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        headerMap.put("tdate", "日期");
        headerMap.put("appName", "应用名称");
        headerMap.put("currDau", "活跃用户");
        headerMap.put("repeatNum", "重叠用户");
        headerMap.put("repeatRate", "重叠率");
        headerMap.put("oldNum", "历史用户");
        headerMap.put("oldRepeatNum", "历史重叠用户");
        headerMap.put("oldRepeatRate", "历史重叠率");

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "重叠用户查询_" + DateTime.now().toString("yyyyMMddHHmmss") + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
    }

    // 微信互推屏蔽配置
    @CrossOrigin
    @RequestMapping(value = "/ad/wxpushFilterHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String wxpushFilterHandle(WxPushFilterVo push, String handle, HttpServletRequest request,
                              HttpServletResponse response) throws IOException, Exception {

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        int result = 0;
        try {
            if (BlankUtils.sqlValidate(push.getId())
                    || BlankUtils.sqlValidate(push.getStatus())) {
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }

            if ("add".equals(handle)) {
                String sql = "insert into wx_push_filter(appid,channel,platform,ftype,filterlist,citycode,status) "
                        + " values(#{obj.appid},#{obj.channel},#{obj.platform},#{obj.ftype},#{obj.filterlist},#{obj.citycode},#{obj.status})";

                result = adService.execSqlHandle(sql, push);
            } else if ("editstatus".equals(handle)) {

                String sql = "update wx_push_filter set status='" + push.getStatus() + "' where id in (" + push.getId() + ")";
                result = adService.execSqlHandle(sql, push);
            } else if ("edit".equals(handle)) {

                String sql = "update wx_push_filter set channel=#{obj.channel},filterlist=#{obj.filterlist},citycode=#{obj.citycode},status=#{obj.status} " +
                        "where id = #{obj.id} ";
                result = adService.execSqlHandle(sql, push);
            } else if ("del".equals(handle)) {

                String sql = "delete from wx_push_filter where id in (" + push.getId() + ")";
                result = adService.execSqlHandle(sql, push);
            } else if ("check".equals(handle)) {
                String where = "";
                if (!BlankUtils.checkBlank(push.getAppid()))
                    where += " and appid = " + push.getAppid();
                if (!BlankUtils.checkBlank(push.getChannel()))
                    where += " and channel like '%" + push.getChannel() + "%'";
                if (!BlankUtils.checkBlank(push.getStatus()))
                    where += " and status = '" + push.getStatus() + "'";

                if (BlankUtils.checkBlank(push.getFtype()))
                    where += " and ftype != '3' ";
                else
                    where += " and ftype = '" + push.getFtype() + "' ";

                String sql = "select * from wx_push_filter where 1=1 " + where;
                List<Map<String, Object>> listMap = adService.queryListMap(sql);
                return "{\"ret\":1,\"data\":" + JSONArray.toJSONString(listMap) + "}";
            }

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }
    
    @CrossOrigin
    @RequestMapping(value = "/ad/wxpushAdconfigHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String wxpushAdconfigHandle(WxPushFilterVo push, String handle, HttpServletRequest request,
                              HttpServletResponse response) throws IOException, Exception {

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        int result = 0;
        try {
            if (BlankUtils.sqlValidate(push.getId())
                    || BlankUtils.sqlValidate(push.getStatus())) {
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }

            if ("add".equals(handle)) {
                String sql = "insert into wx_pushad_config(appid,channel,platform,filterlist,citycode,status,ADParams,cash_flag,ad_config) "
                        + " values(#{obj.appid},#{obj.channel},#{obj.platform},#{obj.filterlist},#{obj.citycode},#{obj.status},#{obj.ADParams},#{obj.cash_flag},#{obj.ad_config})";
                result = adService.execSqlHandle(sql, push);
                
            } else if ("editstatus".equals(handle)) {

                String sql = "update wx_pushad_config set status='" + push.getStatus() + "' where id in (" + push.getId() + ")";
                result = adService.execSqlHandle(sql, push);
            } else if ("edit".equals(handle)) {

                String sql = "update wx_pushad_config set channel=#{obj.channel},filterlist=#{obj.filterlist},citycode=#{obj.citycode},status=#{obj.status},ADParams=#{obj.ADParams},cash_flag=#{obj.cash_flag},ad_config=#{obj.ad_config} " +
                        "where id = #{obj.id} ";
                result = adService.execSqlHandle(sql, push);
            } else if ("del".equals(handle)) {

                String sql = "delete from wx_pushad_config where id in (" + push.getId() + ")";
                result = adService.execSqlHandle(sql, push);
            } else if ("check".equals(handle)) {
                String where = "";
                if (!BlankUtils.checkBlank(push.getAppid()))
                    where += " and appid = " + push.getAppid();
                if (!BlankUtils.checkBlank(push.getChannel()))
                    where += " and channel = '" + push.getChannel() + "'";
                if (!BlankUtils.checkBlank(push.getStatus()))
                    where += " and status = '" + push.getStatus() + "'";

                String sql = "select * from wx_pushad_config where 1=1 " + where;
                List<Map<String, Object>> listMap = adService.queryListMap(sql);
                return "{\"ret\":1,\"data\":" + JSONArray.toJSONString(listMap) + "}";
            }

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    // 产品广告位配置
    @CrossOrigin
    @RequestMapping(value = "/ad/extendAdposConfHandle", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String extendAdposConfHandle(ExtendAdposConfVo push, String handle, HttpServletRequest request,
                                 HttpServletResponse response) throws IOException, Exception {

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        int result = 0;
        try {
            if (BlankUtils.sqlValidate(push.getAdtype())
                    || BlankUtils.sqlValidate(push.getAdpos())) {
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }

            if ("add".equals(handle)) {
                String sql = "insert into test_extend_adpos_conf(appid, appname, adtype, typename, adpos) "
                        + " values(#{obj.appid}, #{obj.appname}, #{obj.adtype}, #{obj.typename}, #{obj.adpos})";

                result = adService.execSqlHandle(sql, push);
            } else if ("edit".equals(handle)) {
                String sql = "update test_extend_adpos_conf set adpos=#{obj.adpos} "
                        + " where appid = #{obj.appid} and adtype = #{obj.adtype}";

                result = adService.execSqlHandle(sql, push);
            } else if ("del".equals(handle)) {
                String sql = "delete from test_extend_adpos_conf where appid = #{obj.appid} and adtype = #{obj.adtype}";
                result = adService.execSqlHandle(sql, push);
            } else if ("check".equals(handle)) {
                String where = "";
                if (!BlankUtils.checkBlank(push.getAppid()))
                    where += " and appid = " + push.getAppid();
                if (!BlankUtils.checkBlank(push.getAdtype()))
                    where += " and adtype = '" + push.getAdtype() + "'";
                if (!BlankUtils.checkBlank(push.getAdpos()))
                    where += " and adpos like '%" + push.getAdpos() + "%'";

                String sql = "select * from test_extend_adpos_conf where 1=1 " + where;
                List<Map<String, Object>> listMap = adService.queryListMap(sql);

                redisTemplate.opsForValue().set("extendAdposConfExcelList", listMap, 20, TimeUnit.MINUTES);
                return "{\"ret\":1,\"data\":" + JSONArray.toJSONString(listMap) + "}";
            }

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    @CrossOrigin
    @RequestMapping(value = "/ad/exportExtendAdposConf", method = RequestMethod.POST)
    public void exportExtendAdposConf(HttpServletRequest request, HttpServletResponse response) {

        List<Map<String, Object>> contentList =
                (List<Map<String, Object>>) redisTemplate.opsForValue().get("extendAdposConfExcelList");

        //数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        headerMap.put("appid", "产品ID");
        headerMap.put("appname", "产品名称");
        headerMap.put("adtype", "广告类型");
        headerMap.put("typename", "类型名称");
        headerMap.put("adpos", "广告位");

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "产品广告位配置_" + DateTime.now().toString("yyyyMMddHHmmss") + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
    }

    // 产品广告位配置三层级联数组
    @CrossOrigin
    @RequestMapping(value = "/ad/getExtendAdposConfList", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String getExtendAdposConfList(HttpServletRequest request) {

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        JSONObject result = new JSONObject();
        try {
            String query = "select * from test_extend_adpos_conf where 1=1";
            List<ExtendAdposConfVo> queryList = adService.queryListBean(query, ExtendAdposConfVo.class);

            // 三层级联下拉列表
            JSONArray array = new JSONArray();
            Map<String, List<ExtendAdposConfVo>> collect = queryList.stream()
                    .collect(Collectors.groupingBy(act -> act.getAppid() + "#" + act.getAppname()));
            collect.forEach((key, val) -> {
                JSONObject obj1 = new JSONObject();
                obj1.put(key.split("#")[0], key.split("#")[1]);

                JSONArray typeSub = new JSONArray();
                Map<String, List<ExtendAdposConfVo>> typeCollect = val.stream()
                        .collect(Collectors.groupingBy(ab -> ab.getAdtype() + "#" + ab.getTypename()));
                typeCollect.forEach((k2, v2) -> {
                    JSONObject obj2 = new JSONObject();
                    obj2.put(k2.split("#")[0], k2.split("#")[1]);

                    JSONArray posSub = new JSONArray();
                    Map<String, List<ExtendAdposConfVo>> adposCollect = v2.stream()
                            .collect(Collectors.groupingBy(ExtendAdposConfVo::getAdpos));
                    adposCollect.forEach((k3, v3) -> {
                        posSub.add(k3);
                    });

                    obj2.put("sub", posSub);
                    typeSub.add(obj2);
                });

                obj1.put("sub", typeSub);
                array.add(obj1);
            });

            result.put("ret", 1);
            result.put("data", array);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误消息：" + e.getMessage());
        }
        return result.toJSONString();
    }

    @CrossOrigin
    @RequestMapping(value = "/ad/selectIosScoreTotal", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String selectIosScoreTotal(GDTWorkReportVo gdt, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {
            // 筛除sql敏感字符注入
            if (BlankUtils.sqlValidate(start_date)
                    || BlankUtils.sqlValidate(end_date)
                    || BlankUtils.sqlValidate(appid)) {
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }
            String yesterday = DateTime.now().minusDays(1).toString("yyyyMMdd");
            if (BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)) {
                start_date = yesterday;
                end_date = yesterday;
            }

            String sql = "select concat(tdate,'') tdate,product_id appid,projectid pid,"
                    + " SUM(add_num) addnum,SUM(give_num) givenum"
                    + " from apk_product_stats"
                    + " where tdate BETWEEN '" + start_date + "' AND '" + end_date + "' "
                    + " and (projectid = 37265007 or projectid = 37719004)"
                    + " group by tdate,projectid"
                    + " order by tdate desc,add_num desc";

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<Map<String, Object>> list = adService.queryListMap(sql);
            long size = ((Page) list).getTotal();
            redisTemplate.opsForValue().set("iosScoreTotalExcelList", list, 20, TimeUnit.MINUTES);

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    @CrossOrigin
    @RequestMapping(value = "/ad/exportIosScoreTotal", method = RequestMethod.POST)
    public void exportIosScoreTotal(HttpServletRequest request, HttpServletResponse response) {

        List<Map<String, Object>> contentList =
                (List<Map<String, Object>>) redisTemplate.opsForValue().get("iosScoreTotalExcelList");

        //数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        headerMap.put("tdate", "日期");
        headerMap.put("appid", "应用ID");
        headerMap.put("pid", "项目ID");
        headerMap.put("addnum", "新增人数");
        headerMap.put("givenum", "投放新增");

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "积分墙投放数据_" + DateTime.now().toString("yyyyMMddHHmmss") + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
    }

    @CrossOrigin
    @RequestMapping(value = "/ad/selectAdmsgAdsidInfo", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String selectAdmsgAdsidInfo(GDTWorkReportVo gdt, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String ad_sid = request.getParameter("ad_sid");
        String pid = request.getParameter("pid");

        // token验证
		/*String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);*/

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {
            // 筛除sql敏感字符注入
            if (BlankUtils.sqlValidate(start_date)
                    || BlankUtils.sqlValidate(end_date)
                    || BlankUtils.sqlValidate(pid)) {
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }
            String today = DateTime.now().toString("yyyyMMdd");
            if (BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)) {
                start_date = today;
                end_date = today;
            }

            String where = "";
            if (!BlankUtils.checkBlank(pid))
                where += " and pid = " + pid;
            if (!BlankUtils.checkBlank(ad_sid))
                where += " and ad_sid like '%" + ad_sid + "%'";

            String sql = "select concat(tdate,'') tdate,pid,ad_sid,"
                    + " SUM(req_num) req_num,SUM(selfshow_num) selfshow_num"
                    + " from admsg_adsid_info"
                    + " where tdate BETWEEN '" + start_date + "' AND '" + end_date + "' " + where
                    + " group by tdate"
                    + " order by tdate desc,req_num desc";

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<Map<String, Object>> list = admsgService.queryListMap(sql);
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    @CrossOrigin
    @RequestMapping(value = "/ad/HaiwaiCDNReadyHot", method = {RequestMethod.POST})
    public @ResponseBody
    String haiwaiCDNReadyHot(@RequestParam(value = "file_name") MultipartFile file,
                             HttpServletRequest request, HttpServletResponse response) throws IOException {

        JSONObject result = new JSONObject();
        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".txt")) {
                Path path = Paths.get(CommonUtil.tempPath + "/" + "cdn_url.txt");
                file.transferTo(path.toFile());
                List<String> lines = Files.readAllLines(path);
                if (lines.size() == 0 || lines.size() > 200) {
                    result.put("ret", 0);
                    result.put("msg", "预热URL请保持在200条以内！");
                    return result.toJSONString();
                }


                String nonce = BlankUtils.getRandomNumber(10);
                String stamp = (DateTime.now().getMillis() / 1000) + "";
                String token = "fc27b7e9-328f-440f-ada1-a133be1daba7";
                // 将 token、 stamp、 nonce、三个参数进行字典序排序
                String[] arr = new String[]{token, stamp, nonce};
                Arrays.sort(arr);

                String url = "http://openapi.fjsdn.com/api/v1/cdn/fetch?client=dnwx"
                        + "&nonce=" + nonce + "&stamp=" + stamp;
                url = url + "&sign=" + DigestUtils.sha1Hex(arr[0] + arr[1] + arr[2]);

                Map<String, String> headMap = new HashMap<String, String>();
                headMap.put("Content-Type", "application/json");

                JSONObject param = new JSONObject();
                JSONArray array = new JSONArray();
                for (String str : lines) {
                    if (BlankUtils.checkBlank(str))
                        continue;
                    JSONObject item = new JSONObject();
                    item.put("uri", str);
                    item.put("md5", "");
                    array.add(item);
                }
                param.put("content", array);

                String httpPost = HttpClientUtils.getInstance()
                        .httpPost(url, param.toJSONString(), headMap);
                JSONObject parseObj = JSONObject.parseObject(httpPost);
                if (parseObj != null && "000000".equals(parseObj.getString("code"))) {
                    result.put("ret", 1);
                    result.put("msg", "预热地址成功");
                } else {
                    System.out.println("cnd预热地址失败：" + parseObj.toJSONString());
                    result.put("ret", 0);

                    if (parseObj != null)
                        result.put("msg", "预热地址失败：" + parseObj.getString("code") + "-" + parseObj.getString("msg"));
                    else
                        result.put("msg", "预热地址失败");
                }
            } else {
                result.clear();
                result.put("ret", 0);
                result.put("msg", "上传文件有误，需要.txt文件!");
            }

        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());

        }
        return result.toJSONString();

//		Path repath = Paths.get(CommonUtil.tempPath+"/"+"cdn_result.txt");
//		Files.write(repath, result, StandardOpenOption.CREATE,StandardOpenOption.TRUNCATE_EXISTING);
//		JxlUtil.outPutExcel("预热结果.txt", repath.toFile(), response);
    }


    @CrossOrigin
    @RequestMapping(value = "/ad/selectProductModel", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String selectProductModel(GDTWorkReportVo gdt, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {
            // 筛除sql敏感字符注入
            if (BlankUtils.sqlValidate(start_date)
                    || BlankUtils.sqlValidate(end_date)
                    || BlankUtils.sqlValidate(appid)) {
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }
            String yesterday = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            if (BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)) {
                start_date = yesterday;
                end_date = yesterday;
            }

            String where = "";
            if (!BlankUtils.checkBlank(appid))
                where += " and productid=" + appid;
            String sql = "select concat(tdate,'') tdate,productid appid,num,model"
                    + " from alone_dau_model"
                    + " where tdate BETWEEN '" + start_date + "' AND '" + end_date + "' " + where
                    + " order by tdate desc,num desc";

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<Map<String, Object>> list = adService.queryListMap(sql);
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    @CrossOrigin
    @RequestMapping(value = "/ad/exportProductModel", method = RequestMethod.POST)
    public void exportProductModel(HttpServletRequest request, HttpServletResponse response) {
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");

        if (BlankUtils.sqlValidate(start_date)
                || BlankUtils.sqlValidate(end_date)
                || BlankUtils.sqlValidate(appid)) {
            return;
        }
        String yesterday = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        if (BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)) {
            start_date = yesterday;
            end_date = yesterday;
        }

        String where = "";
        if (!BlankUtils.checkBlank(appid))
            where += " and productid=" + appid;
        String sql = "select concat(tdate,'') tdate,productid appid,num,model"
                + " from alone_dau_model"
                + " where tdate BETWEEN '" + start_date + "' AND '" + end_date + "' " + where
                + " order by tdate desc,num desc";

        List<Map<String, Object>> contentList = adService.queryListMap(sql);

        //数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        headerMap.put("tdate", "日期");
        headerMap.put("appid", "应用ID");
        headerMap.put("num", "人数");
        headerMap.put("model", "机型");

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "产品机型分布_" + DateTime.now().toString("yyyyMMddHHmmss") + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
    }

}
