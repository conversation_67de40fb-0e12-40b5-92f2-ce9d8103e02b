package com.wbgame.controller.main;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.mapper.master.WbSysV3Mapper;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.product.DnwxX4dataVo;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @description: 表头说明配置
 * @author: caow
 * @date: 2023/10/17
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/main")
public class MenuNoteController {

    @Autowired
    private WbSysV3Mapper wbSysV3Mapper;
    @Autowired
    private YyhzMapper yyhzMapper;


    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/menuNote/list", method={RequestMethod.GET, RequestMethod.POST})
    public String list(DnwxX4dataVo data,HttpServletRequest request,HttpServletResponse response) throws IOException {

        String[] args = {"index","field","note"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);

        JSONObject result = new JSONObject();
        try {

            String query = "select * from main_sys_menu_note where 1=1 ";
            if(!BlankUtils.checkBlank(paramMap.get("index"))){
                query += " and `index` like CONCAT('%',#{obj.index},'%') ";
            }
            if(!BlankUtils.checkBlank(paramMap.get("field"))){
                query += " and `field` like CONCAT('%',#{obj.field},'%') ";
            }
            if(!BlankUtils.checkBlank(paramMap.get("note"))){
                query += " and `note` like CONCAT('%',#{obj.note},'%') ";
            }
            List<Map<String, Object>> list = yyhzMapper.queryListMapTwo(query, paramMap);
            list.forEach(act -> act.put("createtime", act.get("createtime")+""));
            list.forEach(act -> act.put("endtime", act.get("endtime")+""));

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", list.size());
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return result.toJSONString();
    }


    /**
     * 操作
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/menuNote/handle", method={RequestMethod.GET,RequestMethod.POST})
    public String handle(String handle,HttpServletRequest request, HttpServletResponse response) {

        String[] args = {"index","field","note"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);

        // token验证
        String token = request.getParameter("token");
        Object object = redisTemplate.opsForValue().get(token);
        JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(object));
        
        int result = 0;
        try {
            paramMap.put("cuser", json.getString("login_name"));
            paramMap.put("euser", json.getString("login_name"));

            if ("add".equals(handle)) {
                String sql = "insert into yyhz_0308.main_sys_menu_note(`index`,`field`,`note`,cuser,createtime) value(#{obj.index},#{obj.field},#{obj.note},#{obj.cuser},now()) ";
                result = yyhzMapper.execSqlHandle(sql, paramMap);

            } else if ("edit".equals(handle)) {
                String sql = "update yyhz_0308.main_sys_menu_note set `note`=#{obj.note},euser=#{obj.euser},endtime=now() where `index`=#{obj.index} and `field`=#{obj.field} ";
                result = yyhzMapper.execSqlHandle(sql, paramMap);

            } else if ("del".equals(handle)) {
                String sql = "delete from yyhz_0308.main_sys_menu_note where `index`=#{obj.index} and `field`=#{obj.field} ";
                result = yyhzMapper.execSqlHandle(sql, paramMap);
            }

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }


}
