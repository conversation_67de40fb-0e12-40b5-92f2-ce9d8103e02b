package com.wbgame.controller.main;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.master.WbSysV3Mapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.WbPremConfigVo;
import com.wbgame.service.PremService;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @description: 权限中心_新
 * @author: caow
 * @date: 2024/10/28
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/main")
public class WbPremController {

    @Autowired
    private WbSysV3Mapper wbSysV3Mapper;
    @Autowired
    private PremService premService;


    @Autowired
    private RedisTemplate redisTemplate;


    /**
     * 查询菜单
     * @param cur 当前菜单对象
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/menuPrem/list", method = { RequestMethod.GET, RequestMethod.POST })
    public String v3Menu(@RequestParam(value = "start", defaultValue = "1") Integer start,
                         @RequestParam(value = "limit", defaultValue = "999999") Integer limit,
                         WbPremConfigVo prem, HttpServletRequest request, HttpServletResponse response) {

        Map<String, String> paramMap = BlankUtils.getParameter(request, "fm_config_list");

        /* 进行token验证 */
//        String token = request.getParameter("token");
//        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//            return "{\"ret\":2,\"msg\":\"token is error!\"}";
//        else
//            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        try {
            PageHelper.startPage(start, limit);
            List<WbPremConfigVo> list = premService.queryAllPrem(prem);
            long size = ((Page) list).getTotal();

            return ReturnJson.successPage(list);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息 : " + e.getMessage());
        }
    }


    /**
     * 操作菜单配置 新增修改删除
     * @param cur
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/menuPrem/handle", method = { RequestMethod.GET, RequestMethod.POST })
    public String adv2MenuSet(String handle, WbPremConfigVo prem, HttpServletRequest request, HttpServletResponse response) {

        JSONObject result = new JSONObject();
        try {
            /* 进行token验证 */
            String token = request.getParameter("token");
            if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            else
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);


            /* 根据操作标识符确定操作方式 */
            boolean res = false;
            if ("add".equals(handle)) {
                prem.setCuser(cuser.getLogin_name());
                res = premService.addPrem(prem);
            } else if ("edit".equals(handle)) {
                prem.setEuser(cuser.getLogin_name());
                res = premService.updatePrem(prem);
            } else if ("del".equals(handle)) {
                res = premService.deletePrem(prem.getPrem_id());
            } else {
                return "{\"ret\":0,\"msg\":\"handle is error!\"}";
            }

            if(res){
                return ReturnJson.success("操作成功！");
            }else{
                return ReturnJson.toErrorJson("操作失败！");
            }

        } catch (Exception e) {
            e.printStackTrace();

            if(e.toString().contains("Duplicate entry")) {
                result.put("ret", 0);
                result.put("msg", "错误信息 : 标识已存在!");
            }else{
                result.put("ret", 0);
                result.put("msg", "错误信息 : " + e.getMessage());
            }
            return ReturnJson.toErrorJson("错误信息 : " + e.getMessage());
        }
    }

}
