package com.wbgame.controller;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.NpPostVo;
import com.wbgame.pojo.ProdutChannelDataVo;
import com.wbgame.pojo.UmengTotalVo;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.JxlUtil;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.PrintWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.*;
import java.util.concurrent.TimeUnit;

///**
// * 产品数据 -待删除
// * <AUTHOR>
// */
//@Controller
public class AdProductController {

    @Autowired
    SomeService someService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static String endpoint = "https://oss-cn-shenzhen.aliyuncs.com";
    private static String accessKeyId = "LTAImrFvdsZ2E6Vd";
    private static String accessKeySecret = "wWc1X7B8qjWJ1DarzBUyysAT71Dpg2";

    @Value("${tempPath}")
    private String path ;

    /**
     * 产品收支查询
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="/selectProduct", method={RequestMethod.POST})
    public @ResponseBody
    String selectProduct(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try{
            String start = BlankUtils.checkNull(request, "start");
            String limit = BlankUtils.checkNull(request, "limit");

            if(BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(cu.getToken());
            if(cuser == null){
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }else{
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // 当前页
            int pageNo = (pageStart / pageSize) + 1;
            PageHelper.startPage(pageNo, pageSize); // 进行分页
            String um_start_date = BlankUtils.checkNull(request, "um_start_date");
            String um_end_date = BlankUtils.checkNull(request, "um_end_date");
            String handle = BlankUtils.checkNull(request, "handle");
            String product = BlankUtils.checkNull(request, "um_product");
            Map<Object, Object> parmMap = new HashMap<>();
            parmMap.put("start_date", um_start_date);
            parmMap.put("end_date", um_end_date);
            parmMap.put("handle", handle);
            parmMap.put("product", product);
            String uname = cuser.getLogin_name();
            if("gzfg".equals(uname))
                parmMap.put("product", "安卓-黄金切割");
            else if("tanke".equals(uname))
                parmMap.put("product", "坦克进化大作战");
            List<UmengTotalVo> list = someService.selectProduct(parmMap);
            long size = ((Page) list).getTotal();
            JSONObject result = new JSONObject();
            result.put("data", list);
            result.put("ret",1);
            result.put("totalCount", size);
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 修改收支数据
     * @param handle
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="/updateUmengProduct", method={RequestMethod.POST})
    public @ResponseBody
    String updateUmengProduct(String handle, CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try{
            if(BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(cu.getToken());
            if(cuser == null){
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }else{
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            String income = BlankUtils.checkNull(request, "income");
            String expend = BlankUtils.checkNull(request, "expend");
            String id = BlankUtils.checkNull(request, "id");
            Map<Object,Object> map = new HashMap<>();
            map.put("income",income);
            map.put("expend",expend);
            map.put("id",id);
            int ret = someService.updateUmengProduct(map);
            JSONObject result = new JSONObject();
            if(ret > 0){
                result.put("ret",1);
                result.put("msg","操作成功");
            }else {
                result.put("ret",0);
                result.put("msg","操作失败");
            }
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 导出收支数据
     * @param map
     * @param request
     * @param response
     */
    @CrossOrigin
    @RequestMapping(value="/exportProduct", method={RequestMethod.GET,RequestMethod.POST})
    public void exportProduct(ModelMap map, HttpServletRequest request, HttpServletResponse response) {
        //数据头
        Map<String, String> headerMap = new LinkedHashMap<>();

        String um_start_date = BlankUtils.checkNull(request, "um_start_date");
        String um_end_date = BlankUtils.checkNull(request, "um_end_date");
        Map<Object, Object> param= new HashMap<>();
        param.put("start_date", um_start_date);
        param.put("end_date", um_end_date);
        //数据内容
        List<UmengTotalVo> list = someService.selectProduct(param);
        Map<String, String> inMap = new LinkedHashMap<>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<>();
        Map<String, Object> contentMap;
        for(UmengTotalVo temp : list){
            headerMap.put("tdate","日期");
            headerMap.put("product","产品名称");
            headerMap.put("channel","渠道名称");
            headerMap.put("ctype","渠道类型");
            headerMap.put("addnum","新增");
            headerMap.put("actnum","活跃");
            headerMap.put("income","收入");
            headerMap.put("expend","支出");

            contentMap = new LinkedHashMap<>();
            contentMap.put("tdate",temp.getTdate());
            contentMap.put("product",temp.getProduct());
            contentMap.put("channel",temp.getChannel()==null?"全部":temp.getChannel());
            contentMap.put("ctype",temp.getCtype()==1?"买量渠道":"市场渠道");
            contentMap.put("addnum",temp.getAddnum());
            contentMap.put("actnum",temp.getActnum());
            contentMap.put("income",temp.getIncome());
            contentMap.put("expend",temp.getExpend());

            contentList.add(contentMap);
        }

        String fileName = "收支数据_"+ DateTime.now().toString("yyyyMMdd")+".xls";
        //获得指定文件的物理路径
        //String path = request.getRealPath(JxlUtil.DEFAULTDIR + "/" + fileName);
        JxlUtil.doSave(fileName,headerMap,contentList,inMap,null,null,request,response);
    }

    /**
     * 分类收入查询
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="/selectIncomeData", method={RequestMethod.POST})
    public @ResponseBody
    String selectIncomeData(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try{
            String start = BlankUtils.checkNull(request, "start");
            String limit = BlankUtils.checkNull(request, "limit");

            if(BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(cu.getToken());
            if(cuser == null){
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }else{
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // 当前页
            int pageNo = (pageStart / pageSize) + 1;
            PageHelper.startPage(pageNo, pageSize); // 进行分页
            String um_start_date = BlankUtils.checkNull(request, "um_start_date");
            String um_end_date = BlankUtils.checkNull(request, "um_end_date");
            String um_product = BlankUtils.checkNull(request, "um_product");
            String um_ctype = BlankUtils.checkNull(request, "um_ctype");
            Map<Object, Object> parmMap = new HashMap<>();
            parmMap.put("start_date", um_start_date);
            parmMap.put("end_date", um_end_date);
            parmMap.put("product", um_product);
            String uname = cuser.getLogin_name();
            if("gzfg".equals(uname))
                parmMap.put("product", "安卓-黄金切割");
            else if("tanke".equals(uname))
                parmMap.put("product", "坦克进化大作战");
            parmMap.put("ctype", um_ctype);
            List<UmengTotalVo> list = someService.selectIncomeData(parmMap);

            long size = ((Page) list).getTotal();
            JSONObject result = new JSONObject();
            result.put("data", list);
            result.put("ret",1);
            result.put("totalCount", size);
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 导出分类收入数据
     * @param cu
     * @param map
     * @param request
     * @param response
     */
    @CrossOrigin
    @RequestMapping(value="/exportIncomeData", method={RequestMethod.GET,RequestMethod.POST})
    public void exportIncomeData(CurrUserVo cu,ModelMap map, HttpServletRequest request, HttpServletResponse response) {
        //数据头
        Map<String, String> headerMap = new LinkedHashMap<>();
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(cu.getToken());
        String um_start_date = BlankUtils.checkNull(request, "um_start_date");
        String um_end_date = BlankUtils.checkNull(request, "um_end_date");
        Map<Object, Object> param= new HashMap<>();
        param.put("start_date", um_start_date);
        param.put("end_date", um_end_date);
        String uname = cuser.getLogin_name();
        if("gzfg".equals(uname))
            param.put("product", "安卓-黄金切割");
        else if("tanke".equals(uname))
            param.put("product", "坦克进化大作战");
        //数据内容
        List<UmengTotalVo> list = someService.selectIncomeData(param);
        Map<String, String> inMap = new LinkedHashMap<>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<>();
        Map<String, Object> contentMap;
        for(UmengTotalVo temp : list){
            headerMap.put("tdate","日期");
            headerMap.put("product","产品名称");
            headerMap.put("ctype","渠道类型");
            headerMap.put("addnum","新增");
            headerMap.put("actnum","活跃");
            headerMap.put("income","收入");
            headerMap.put("expend","支出");

            contentMap = new LinkedHashMap<>();
            contentMap.put("tdate",temp.getTdate());
            contentMap.put("product",temp.getProduct());
            contentMap.put("ctype",temp.getCtype()==1?"买量渠道":"市场渠道");
            contentMap.put("addnum",temp.getAddnum());
            contentMap.put("actnum",temp.getActnum());
            contentMap.put("income",temp.getIncome());
            contentMap.put("expend",temp.getExpend());

            contentList.add(contentMap);
        }

        String fileName = "分类收入数据_"+ DateTime.now().toString("yyyyMMdd")+".xls";
        //获得指定文件的物理路径
        //String path = request.getRealPath(JxlUtil.DEFAULTDIR + "/" + fileName);
        JxlUtil.doSave(fileName,headerMap,contentList,inMap,null,null,request,response);
    }



}