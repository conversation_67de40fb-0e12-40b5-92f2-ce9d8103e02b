package com.wbgame.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.ShortUrlVo;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.Encript;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.concurrent.TimeUnit;

///**
// * 短链控制类 -待删除
// * <AUTHOR>
// */
//@Controller
public class ShortUrlController {

    public static Integer CHARNUM = 6;

    @Autowired
    SomeService someService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @CrossOrigin
    @RequestMapping(value="/short/*", method={RequestMethod.GET})
    public @ResponseBody
    void shortUrl(HttpServletRequest request, HttpServletResponse response){
        String url = request.getRequestURI();
//        System.out.println(url);
        String[] urls = url.split("/");
        List<ShortUrlVo> list = someService.selectShortUrl();
        if(list != null){
            list.stream().filter(shortUrlVo -> urls[2].equals(shortUrlVo.getShortkey())).forEach(shortUrlVo -> {
                try {
                    response.sendRedirect(shortUrlVo.getUrlvalue());
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
        }
    }

    /**
     * 增加短链配置
     * @param request
     * @param response
     */
    @CrossOrigin
    @RequestMapping(value="/addShort", method={RequestMethod.POST})
    public @ResponseBody
    String addShortUrl(HttpServletRequest request, HttpServletResponse response){
        String replace = request.getParameter("value").replace(" ", "+");
        String value = null;
        try {
            value = new String(Base64.decodeBase64(replace),"UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        //System.out.println("接收参数===="+value);
        JSONObject obj = JSONObject.parseObject(value);
        if(BlankUtils.checkBlank(obj.getString("token"))
                || BlankUtils.checkBlank(obj.getString("url"))){
            return "{\"ret\":0,\"msg\":\"request value is error!\"}";
        }

        CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(obj.getString("token"));
        if(cuser == null){
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        }else{
            redisTemplate.opsForValue()
                    .set(obj.getString("token"), cuser, 20 * 60, TimeUnit.SECONDS);
        }
        String url = (String) obj.get("url");
        JSONObject result = new JSONObject();
        if(url != null){
            String key = getRandomStr(6);
            ShortUrlVo shortUrlVo = new ShortUrlVo();
            shortUrlVo.setShortkey(key);
            shortUrlVo.setUrlvalue(url);

            int res = someService.insertShortUrl(shortUrlVo);
            if(res > 0){
                result.put("ret", 1);
                result.put("msg", "添加成功");
            }else {
                result.put("ret", 0);
                result.put("msg", "添加异常");
            }
        }else {
            result.put("ret", 0);
            result.put("msg", "缺少参数");
        }
        return result.toJSONString();
    }

    /**
     * 展示短链
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="/selectShort", method={RequestMethod.GET})
    public @ResponseBody
    String selectShortUrl(CurrUserVo cu,HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        if(BlankUtils.checkBlank(cu.getToken()))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(cu.getToken());
        if(cuser == null){
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        }else{
            redisTemplate.opsForValue()
                    .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
        }

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 当前页
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {
            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<ShortUrlVo> list = someService.selectShortUrlAll();
            for(ShortUrlVo shortUrlVo : list){
                shortUrlVo.setShortkey("https://api.vzhifu.net/short/" + shortUrlVo.getShortkey());
            }
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误消息："+e.getMessage());
        }

        return result.toJSONString();
    }

    /**
     * 随机生成6位字符串
     * @param num
     * @return
     */
    public static String getRandomStr(int num){
        String result = "";
        for (int i = 0; i<num; i++) {
           int intVal = (int)(Math.random()*26+97);
           result = result +(char) intVal;
         }
        return result;
    }

    /**
     * 短链生成
     * @param url
     * @return
     */
    public String shortUrl(String url) {
        // 可以自定义生成 MD5 加密字符传前的混合 KEY
        String key = "dnwx";
        // 要使用生成 URL 的字符
        String[] chars = new String[]{"a", "b", "c", "d", "e", "f", "g", "h",
                "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t",
                "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5",
                "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H",
                "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T",
                "U", "V", "W", "X", "Y", "Z"
        };
        // 对传入网址进行 MD5 加密
        String sMD5EncryptResult = (Encript.md5(key + url));
        String hex = sMD5EncryptResult;
        String resUrl;
        //得到 4组短链接字符串
        //for (int i = 0; i < 4; i++) {
        // 把加密字符按照 8 位一组 16 进制与 0x3FFFFFFF 进行位与运算
        String sTempSubString = hex.substring(0 * 8, 0 * 8 + 8);
        // 这里需要使用 long 型来转换，因为 Inteper .parseInt() 只能处理 31 位 , 首位为符号位 , 如果不用 long ，则会越界
        long lHexLong = 0x3FFFFFFF & Long.parseLong(sTempSubString, 16);
        String outChars = "";
        //循环获得每组6位的字符串
        for (int j = 0; j < CHARNUM; j++) {
            // 把得到的值与 0x0000003D 进行位与运算，取得字符数组 chars 索引(具体需要看chars数组的长度   以防下标溢出，注意起点为0)
            long index = 0x0000003D & lHexLong;
            // 把取得的字符相加
            outChars += chars[(int) index];
            // 每次循环按位右移 5 位
            lHexLong = lHexLong >> 5;
        }
        // 把字符串存入对应索引的输出数组
        resUrl = outChars;
        //}
        return resUrl;
    }


}