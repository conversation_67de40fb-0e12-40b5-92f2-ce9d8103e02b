package com.wbgame.controller.product;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.aop.LoginCheck;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.product.SingularLandingConfig;
import com.wbgame.service.product.SingularLandingConfigService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Singular落地页配置Controller
 * @Date 2025/06/27
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/product/singular")
@Api(tags = "Singular落地页配置管理")
public class SingularLandingConfigController {

    private static final Logger logger = LoggerFactory.getLogger(SingularLandingConfigController.class);

    @Autowired
    private SingularLandingConfigService singularLandingConfigService;

    /**
     * 分页查询配置列表
     *
     * @param config 查询条件
     * @return 分页结果
     */
    @ApiOperation(value = "分页查询配置列表", notes = "分页查询Singular落地页配置列表")
    @GetMapping("/list")
    @LoginCheck
    public String queryList(@RequestBody SingularLandingConfig config) {
        try {
            // 参数校验
            config.checkPageParams();

            // 分页查询
            PageHelper.startPage(config.getStart(), config.getLimit());
            List<SingularLandingConfig> list = singularLandingConfigService.queryListData(config);

            return ReturnJson.successPage(list);
        } catch (Exception e) {
            logger.error("查询Singular落地页配置列表失败", e);
            return ReturnJson.toErrorJson("查询失败");
        }
    }

    /**
     * 根据ID查询配置详情
     *
     * @param id 主键ID
     * @return 配置详情
     */
    @ApiOperation(value = "查询配置详情", notes = "根据ID查询Singular落地页配置详情")
    @GetMapping("/detail/{id}")
    @LoginCheck
    public String queryById(@ApiParam(value = "配置ID", required = true) @PathVariable Long id) {
        try {
            if (id == null || id <= 0) {
                return ReturnJson.toErrorJson("参数错误");
            }

            SingularLandingConfig config = singularLandingConfigService.queryByIdData(id);
            if (config == null) {
                return ReturnJson.toErrorJson("配置不存在");
            }

            return ReturnJson.success(config);
        } catch (Exception e) {
            logger.error("根据ID查询Singular落地页配置失败，ID: {}", id, e);
            return ReturnJson.toErrorJson("查询失败");
        }
    }

    /**
     * 新增配置
     *
     * @param request HttpServletRequest
     * @param config  配置信息
     * @return 操作结果
     */
    @ApiOperation(value = "新增配置", notes = "新增Singular落地页配置")
    @PostMapping("/insert")
    @LoginCheck
    public String insert(HttpServletRequest request, @RequestBody SingularLandingConfig config) {
        try {
            CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
            if (loginUser != null) {
                config.setCreateUser(loginUser.getLogin_name());
            }

            String validateResult = singularLandingConfigService.validateConfigData(config, false);
            if (validateResult != null) {
                return ReturnJson.toErrorJson(validateResult);
            }

            // 设置默认值
            if (config.getStatus() == null) {
                config.setStatus(1);
            }

            int result = singularLandingConfigService.insertData(config);
            if (result > 0) {
                return ReturnJson.success("新增成功");
            } else {
                return ReturnJson.toErrorJson("新增失败");
            }
        } catch (Exception e) {
            logger.error("新增Singular落地页配置失败", e);
            return ReturnJson.toErrorJson("新增失败");
        }
    }

    /**
     * 更新配置
     *
     * @param request HttpServletRequest
     * @param config  配置信息
     * @return 操作结果
     */
    @ApiOperation(value = "更新配置", notes = "更新Singular落地页配置")
    @PostMapping("/update")
    @LoginCheck
    public String update(HttpServletRequest request, @RequestBody SingularLandingConfig config) {
        try {
            if (config.getId() == null || config.getId() <= 0) {
                return ReturnJson.toErrorJson("参数错误");
            }

            CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
            if (loginUser != null) {
                config.setUpdateUser(loginUser.getLogin_name());
            }

            int result = singularLandingConfigService.updateData(config);
            if (result > 0) {
                return ReturnJson.success("更新成功");
            } else {
                return ReturnJson.toErrorJson("更新失败");
            }
        } catch (Exception e) {
            logger.error("更新Singular落地页配置失败", e);
            return ReturnJson.toErrorJson("更新失败");
        }
    }


    /**
     * 批量删除配置
     *
     * @param ids 主键ID列表
     * @return 操作结果
     */
    @ApiOperation(value = "批量删除配置", notes = "批量删除Singular落地页配置")
    @PostMapping("/batchDelete")
    @LoginCheck
    public String batchDelete(@RequestBody List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return ReturnJson.toErrorJson("参数错误");
            }

            int result = singularLandingConfigService.batchDeleteData(ids);
            return ReturnJson.success("批量删除成功，共删除" + result + "条记录");
        } catch (Exception e) {
            logger.error("批量删除Singular落地页配置失败", e);
            return ReturnJson.toErrorJson("批量删除失败");
        }
    }

    /**
     * 根据URL参数获取配置（供落地页使用，无需登录）
     *
     * @param sdkKey   SDK Key（可选）
     * @param bundleId Bundle ID（可选）
     * @return 配置信息
     */
    @ApiOperation(value = "根据URL参数获取配置", notes = "根据URL参数获取Singular落地页配置，优先使用SDK Key，其次使用Bundle ID")
    @GetMapping("/config")
    public String getConfig(
            @ApiParam(value = "SDK Key") @RequestParam(required = false) String sdkKey,
            @ApiParam(value = "Bundle ID") @RequestParam(required = false) String bundleId) {

        try {
            // 优先使用SDK Key
            if (sdkKey != null && !sdkKey.trim().isEmpty()) {
                SingularLandingConfig config = singularLandingConfigService.getConfigBySdkKeyData(sdkKey.trim());
                if (config != null) {
                    return ReturnJson.success(config);
                }
            }

            // 其次使用Bundle ID
            if (bundleId != null && !bundleId.trim().isEmpty()) {
                SingularLandingConfig config = singularLandingConfigService.getConfigByBundleIdData(bundleId.trim());
                if (config != null) {
                    return ReturnJson.success(config);
                }
            }

            // 参数都为空或配置不存在时返回错误
            return ReturnJson.toErrorJson("SDK Key和Bundle ID不能都为空或配置不存在");
        } catch (Exception e) {
            logger.error("根据URL参数获取Singular落地页配置失败，sdkKey: {}, bundleId: {}", sdkKey, bundleId, e);
            return ReturnJson.toErrorJson("查询失败");
        }
    }

    /**
     * 导出配置列表
     *
     * @param response HttpServletResponse
     * @param config   查询条件
     */
    @ApiOperation(value = "导出配置列表", notes = "导出Singular落地页配置列表到Excel")
    @PostMapping("/export")
    @LoginCheck
    public void export(HttpServletRequest request,HttpServletResponse response, @RequestBody SingularLandingConfig config) {
        try {
            // 查询数据
            List<SingularLandingConfig> list = singularLandingConfigService.queryListData(config);

            if (list == null || list.isEmpty()) {
                return;
            }


            String value = request.getParameter("value");
            Map<String,String> headerMap = new LinkedHashMap<>();
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }

            // 生成文件名
            String fileName = "Singular落地页配置_" + DateTime.now().toString("yyyyMMddHHmmss") + ".xlsx";

            // 导出Excel
            ExportExcelUtil.exportXLSX2(response, list, headerMap, fileName);

        } catch (Exception e) {
            logger.error("导出Singular落地页配置失败", e);
        }
    }
}
